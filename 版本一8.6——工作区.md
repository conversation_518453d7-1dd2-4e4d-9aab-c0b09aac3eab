
**1. 一种基于自适应频谱阈值的肠鸣音信号检测方法，其特征在于，具体步骤包括：**

**步骤1**、构建肠鸣音信号数据集 $\mathrm{B} = \{\mathrm{X}, \mathrm{T}\}$，其中，$\mathrm{X} = \{x_1, x_2, \ldots, x_n\}$ 表示一段时间内的肠鸣音信号数据序列，$\mathrm{T} = \{t_1, t_2, \ldots, t_n\}$ 表示对应的时间向量序列；令检测到的肠鸣音事件标签集合为 $\mathrm{Y} = \{y_1, y_2, \ldots, y_m\}$，其中 $y_i \in \{\mathrm{SB}, \mathrm{MB}, \mathrm{CRS}\}$，分别表示单次爆发、多次爆发和连续性随机，m为检测到的事件总数；

**步骤1**、构建肠鸣音信号数据集，获取原始信号数据序列$\mathrm{X} = \{x_1, x_2, \ldots, x_n\}$，其中$x_i$表示第$i$个采样点的信号幅值，$n$为信号总采样点数；同时记录对应的时间向量序列$\mathrm{T} = \{t_1, t_2, \ldots, t_n\}$，其中$t_i = (i-1)/F_s$，$F_s$为采样频率；将原始信号数据和时间信息组合构成肠鸣音信号数据集$\mathrm{B} = \{\mathrm{X}, \mathrm{T}, F_s\}$，为后续频谱分析提供输入数据；


**步骤2**、将所述肠鸣音信号数据集$\mathrm{B}$中的信号数据序列$\mathrm{X}$进行频谱图计算，采用短时傅里叶变换获得功率谱密度矩阵$\mathrm{P}(f, t)$，并计算声音强度序列$\mathrm{I}(t)$，其中，$f$为频率，$t$为时间；

**步骤2**、对肠鸣音信号数据序列 $\mathrm{X}$ 进行时频分析，采用短时傅里叶变换计算功率谱密度矩阵，具体过程如下：选择汉宁窗函数，设置窗口长度为 $L = 0.025 \times F_s$ 个采样点，重叠率为50%；对信号序列 $\mathrm{X}$ 进行分帧处理，计算每帧的傅里叶变换，获得功率谱密度矩阵 $\mathrm{P}(f, t)$，其中 $f$ 为频率轴，$t$ 为时间轴；基于功率谱密度矩阵计算声音强度序列：
$$\mathrm{I}(t) = \sum_{f=f_{min}}^{f_{max}} \mathrm{P}(f, t)$$
其中，$f_{min}$和$f_{max}$分别为肠鸣音信号的有效频率范围下限和上限，通常设置为50Hz和2000Hz；


**步骤3**、 基于所述声音强度序列$\mathrm{I}(t)$计算信号统计特征，获得声音强度序列的信号均值$\mathrm{\mu_I}$和信号标准差$\mathrm{\sigma_I}$，其中，$N$为声音强度序列的样本数量，$\mathrm{I}(t_i)$为第$i$个时刻的声音强度值；

**步骤4**、 基于所述信号统计特征设置多类型肠鸣音的差异化自适应阈值，依据的公式如下：

$$
\mathrm{Th_{SB}} = \mathrm{\mu_I} + \alpha \cdot \mathrm{\sigma_I}
$$

$$
\mathrm{Th_{CRS}} = \mathrm{\mu_I} + \alpha_{crs} \cdot \mathrm{\sigma_I}
$$

其中，$\mathrm{Th_{SB}}$为短脉冲音SB事件的检测阈值，$\mathrm{Th_{CRS}}$为连续性声音CRS事件的检测阈值，$\alpha$为SB阈值调节参数，取值范围为0.05-0.15，优选值为0.08，$\alpha_{crs}$为CRS阈值调节参数，取值范围为0.02-0.08，优选值为0.05，且满足$\alpha_{crs} < \alpha$的约束条件，所述差异化阈值设置实现不同类型肠鸣音事件的敏感度控制；

**步骤5**、 基于所述SB检测阈值$\mathrm{Th_{SB}}$进行SB候选事件的检测、边界延伸和密度过滤处理，具体包括以下子步骤：

**5.1 事件边界检测和时间范围筛选**

当$\mathrm{I}(t) > \mathrm{Th_{SB}}$时，标识为候选区域；利用差分运算检测事件边界：

$$
\mathrm{E_{start}} = \{t_i | \mathrm{I}(t_i) \leq \mathrm{Th_{SB}} \text{ 且 } \mathrm{I}(t_{i+1}) > \mathrm{Th_{SB}}\}
$$

$$
\mathrm{E_{end}} = \{t_j | \mathrm{I}(t_j) > \mathrm{Th_{SB}} \text{ 且 } \mathrm{I}(t_{j+1}) \leq \mathrm{Th_{SB}}\}
$$

筛选事件持续时间$\mathrm{T_{duration}} = \mathrm{E_{end}} - \mathrm{E_{start}}$在$\mathrm{T_{min}}$-$\mathrm{T_{max}}$范围内的事件作为SB候选事件，对所述SB候选事件进行峰值强度验证，要求峰值强度$\mathrm{I_{peak}} > \beta \times \mathrm{Th_{SB}}$；

**5.2 事件边界延伸处理**

事件边界延伸样本数计算公式：

$$
\mathrm{N_{ext}} = \text{round}(0.003 \times \mathrm{F_s})
$$

边界延伸处理：

$$
\mathrm{E_{start}^{ext}} = \max(1, \mathrm{E_{start}} - \mathrm{N_{ext}})
$$

$$
\mathrm{E_{end}^{ext}} = \min(\mathrm{N_{total}}, \mathrm{E_{end}} + \mathrm{N_{ext}})
$$

其中，$\mathrm{N_{ext}}$为边界延伸样本数，$\mathrm{F_s}$为采样频率，$\mathrm{N_{total}}$为信号总样本数；

**5.3 密度过滤处理**

在时间窗口$\mathrm{T_{window}}$内限制事件数量不超过$\mathrm{N_{max}}$个，对于任意SB候选事件$i$，定义其时间窗口为$[\mathrm{T_{start,i}}, \mathrm{T_{start,i}} + \mathrm{T_{window}}]$，统计该窗口内的事件数量$\mathrm{N_{count}}$：

$$
\mathrm{N_{count}} = |\{j | \mathrm{T_{start,i}} \leq \mathrm{T_{start,j}} \leq \mathrm{T_{start,i}} + \mathrm{T_{window}}\}|
$$

当$\mathrm{N_{count}} \leq \mathrm{N_{max}}$时，保留事件$i$；当$\mathrm{N_{count}} > \mathrm{N_{max}}$时，移除事件$i$；其中，窗口大小$\mathrm{T_{window}}$为1-10秒，最大事件数$\mathrm{N_{max}}$为5-15个，所述密度过滤确保检测结果的合理性和稳定性；

**步骤6**、 多次爆发的识别：对所述SB候选事件进行序列模式识别，采用贪婪搜索算法检测时间间隙在设定范围内的多个SB事件，将符合条件的SB序列标记为多次爆发MB事件，具体包括以下子步骤：

**6.1 SB事件排序和初始化**

对SB事件按时间排序，初始化事件类型标记$\mathrm{Type_i} = \text{"SB"}$，为后续的序列搜索做准备；

**6.2 基于贪婪策略的序列扩展**

采用贪婪扩展策略，从每个未分类的SB事件开始向后搜索符合间隙条件的后续SB事件，计算相邻SB事件间的时间间隙：

$$
\mathrm{Gap_{i,j}} = \mathrm{T_{start,j}} - \mathrm{T_{end,i}}
$$

当$\mathrm{Gap_{min}} \leq \mathrm{Gap_{i,j}} \leq \mathrm{Gap_{max}}$时，将SB事件$j$添加到序列中，形成候选MB序列；

**6.3 序列验证和筛选**

对形成的SB序列进行验证，同时满足以下条件的序列标记为MB事件：

序列长度验证：$\mathrm{N_{min}} \leq |\mathrm{Sequence}| \leq \mathrm{N_{max}}$

序列总持续时间验证：$\mathrm{T_{min}} \leq (\mathrm{T_{end,last}} - \mathrm{T_{start,first}}) \leq \mathrm{T_{max}}$

**6.4 MB事件合并处理**

将验证通过的SB序列合并为单个MB事件，计算MB事件的起始时间、结束时间和强度特征，替换原有的独立SB事件；

其中，$\mathrm{Gap_{min}}$为SB间最小间隙，取值范围-8至5毫秒，$\mathrm{Gap_{max}}$为SB间最大间隙，取值范围50至150毫秒，$\mathrm{N_{min}}$为序列最少SB数量，设定为2个，$\mathrm{N_{max}}$为序列最多SB数量，取值范围5至8个，$\mathrm{T_{min}}$为MB最短总持续时间，取值范围30至50毫秒，$\mathrm{T_{max}}$为MB最长总持续时间，取值范围1000至2000毫秒；

**步骤7**、 连续性声音的检测与事件优先级管理：基于CRS检测阈值进行连续性声音事件的独立检测，并与SB/MB事件进行智能重叠检测和替代处理，具体包括以下子步骤：

**7.1 CRS事件边界检测和时间筛选**

检测超过CRS阈值的信号区域：

$$
\mathrm{Mask_{CRS}} = \{t_i | \mathrm{I}(t_i) > \mathrm{Th_{CRS}}\}
$$

利用差分运算检测CRS事件边界：

$$
\mathrm{E_{CRS,start}} = \{t_i | \mathrm{I}(t_i) \leq \mathrm{Th_{CRS}} \text{ 且 } \mathrm{I}(t_{i+1}) > \mathrm{Th_{CRS}}\}
$$

$$
\mathrm{E_{CRS,end}} = \{t_j | \mathrm{I}(t_j) > \mathrm{Th_{CRS}} \text{ 且 } \mathrm{I}(t_{j+1}) \leq \mathrm{Th_{CRS}}\}
$$

**7.2 CRS事件边界延伸处理**

对CRS事件进行边界延伸处理：

$$
\mathrm{E_{CRS,start}^{ext}} = \max(1, \mathrm{E_{CRS,start}} - \mathrm{N_{ext}})
$$

$$
\mathrm{E_{CRS,end}^{ext}} = \min(\mathrm{N_{total}}, \mathrm{E_{CRS,end}} + \mathrm{N_{ext}})
$$

对计算得到的CRS事件持续时间$\mathrm{T_{CRS}} = \mathrm{E_{CRS,end}^{ext}} - \mathrm{E_{CRS,start}^{ext}}$进行筛选，保留满足$50\text{ms} \leq \mathrm{T_{CRS}} \leq 3000\text{ms}$的事件作为CRS候选事件；

**7.3 智能重叠检测**

设定重叠容差$\mathrm{\delta_{overlap}} = 0.01$秒，对所有CRS事件和SB/MB事件进行双重循环重叠检测，定义两种重叠情况：

包含关系检测：
$$
\text{Contains}(i,j) = (\mathrm{T_{CRS,start,i}} \leq \mathrm{T_{SB/MB,start,j}} + \mathrm{\delta_{overlap}}) \land (\mathrm{T_{CRS,end,i}} \geq \mathrm{T_{SB/MB,end,j}} - \mathrm{\delta_{overlap}})
$$

部分重叠检测：
$$
\text{Overlaps}(i,j) = (\mathrm{T_{CRS,start,i}} < \mathrm{T_{SB/MB,end,j}} - \mathrm{\delta_{overlap}}) \land (\mathrm{T_{CRS,end,i}} > \mathrm{T_{SB/MB,start,j}} + \mathrm{\delta_{overlap}})
$$

**7.4 事件优先级替代处理**

事件替代决策：当$\text{Contains}(i,j) \lor \text{Overlaps}(i,j)$为真时，标记SB/MB事件$j$为待移除事件；

事件列表更新：移除所有被标记的SB/MB事件，将CRS事件添加到最终事件列表中，并按时间顺序重新排列所有事件；

其中，$\mathrm{T_{CRS,start,i}}$和$\mathrm{T_{CRS,end,i}}$分别为CRS事件$i$的起始和结束时间，$\mathrm{T_{SB/MB,start,j}}$和$\mathrm{T_{SB/MB,end,j}}$分别为SB/MB事件$j$的起始和结束时间，所述智能重叠检测实现CRS事件优先级高于SB/MB事件的替代机制；

**步骤8**、 生成最终检测结果输出，将最终事件列表转换为标准输出格式，生成事件类型标签数组和时间位置矩阵：

$$
\mathrm{labelVals} = \{\mathrm{Type_i} | i = 1, 2, \ldots, N_{total}\}
$$

$$
\mathrm{labelLocs} = \begin{bmatrix}
\mathrm{T_{start,1}} & \mathrm{T_{end,1}} \\
\mathrm{T_{start,2}} & \mathrm{T_{end,2}} \\
\vdots & \vdots \\
\mathrm{T_{start,N_{total}}} & \mathrm{T_{end,N_{total}}}
\end{bmatrix}
$$

对检测结果进行分类计数，计算各类型事件的数量：

$$
\mathrm{N_{SB}} = \sum_{i=1}^{N_{total}} \mathbf{1}(\mathrm{Type_i} = \text{"SB"})
$$

$$
\mathrm{N_{MB}} = \sum_{i=1}^{N_{total}} \mathbf{1}(\mathrm{Type_i} = \text{"MB"})
$$

$$
\mathrm{N_{CRS}} = \sum_{i=1}^{N_{total}} \mathbf{1}(\mathrm{Type_i} = \text{"CRS"})
$$

其中，$\mathrm{Type_i} \in \{\text{"SB"}, \text{"MB"}, \text{"CRS"}\}$为事件类型标识，$N_{total}$为检测到的总事件数，$\mathbf{1}(\cdot)$为指示函数。
