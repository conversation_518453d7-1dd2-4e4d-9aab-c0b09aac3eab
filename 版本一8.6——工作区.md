
**符号说明：**
为避免混淆，本方法中使用以下时间变量符号：

**原始时间轴（步骤1）：**
- $t_i$：第$i$个采样点的时间戳，$t_i = (i-1)/F_s$，$i = 1, 2, \ldots, n$
- $\mathrm{T} = \{t_1, t_2, \ldots, t_n\}$：原始采样时间向量
- 时间分辨率：$\Delta t = 1/F_s$

**STFT时间轴（步骤2及后续）：**
- $\tau_k$：第$k$个STFT时间窗口的中心时间，$k = 1, 2, \ldots, m$
- $\mathrm{T_{STFT}} = \{\tau_1, \tau_2, \ldots, \tau_m\}$：STFT时间向量
- 时间分辨率：$\Delta\tau = H/F_s$，其中$H$为STFT跳跃步长

**时间轴对应关系：**
- 索引映射：$\tau_k$对应原始采样点索引范围$[(k-1) \cdot H + 1, (k-1) \cdot H + L]$
- 数值映射：$\tau_k = t_{(k-1) \cdot H + \lceil L/2 \rceil}$，其中$L$为STFT窗口长度
- 分辨率关系：$\Delta\tau = H \cdot \Delta t$，即STFT时间分辨率是原始时间分辨率的$H$倍
- 样本数关系：$m = \lfloor \frac{n-L}{H} \rfloor + 1 \leq n$.

**STFT时间轴与原始时间轴的数学关系**：设STFT窗口长度为 $L$ 个采样点，跳跃步长为 $H$ 个采样点，则STFT时间轴 $\mathrm{T_{STFT}} = \{\tau_1, \tau_2, \ldots, \tau_m\}$ 与原始时间轴的对应关系为：
$$\tau_k = t_{(k-1) \cdot H + \lceil L/2 \rceil} = \frac{(k-1) \cdot H + \lceil L/2 \rceil - 1}{F_s}$$
其中，$k = 1, 2, \ldots, m$，$m = \lfloor \frac{n-L}{H} \rfloor + 1$为STFT时间点总数，$\lceil \cdot \rceil$为向上取整函数，$\tau_k$表示第$k$个时间窗口的中心时间；

**时间分辨率转换**：STFT时间分辨率为$\Delta\tau = \frac{H}{F_s}$，原始采样时间分辨率为$\Delta t = \frac{1}{F_s}$，因此时间分辨率比值为$\frac{\Delta\tau}{\Delta t} = H$；

**1. 一种基于自适应频谱阈值的肠鸣音信号检测方法，其特征在于，具体步骤包括：**

**步骤1**、构建肠鸣音信号数据集，获取原始信号数据序列$\mathrm{X} = \{x_1, x_2, \ldots, x_n\}$，其中$x_i$表示第$i$个采样点的信号幅值，$n$为信号总采样点数；同时记录对应的时间向量序列$\mathrm{T} = \{t_1, t_2, \ldots, t_n\}$，其中$t_i = (i-1)/F_s$，$F_s$为采样频率；将原始信号数据和时间信息组合构成肠鸣音信号数据集$\mathrm{B} = \{\mathrm{X}, \mathrm{T}, F_s\}$，为后续频谱分析提供输入数据；其中$t_i = (i-1)/F_s$表示第$i$个采样点对应的时间戳，$i=1,2,\ldots,n$，采样从$t=0$时刻开始，采样间隔为$1/F_s$秒；

**步骤2**、对所述肠鸣音信号数据序列$\mathrm{X}$进行时频分析，采用短时傅里叶变换获得功率谱密度矩阵$\mathrm{P}(f, \tau)$，其中$f$为频率，$\tau$为STFT处理后的时间轴；基于所述功率谱密度矩阵计算声音强度序列：
$$\mathrm{I}(\tau) = \sum_{f=f_{min}}^{f_{max}} \mathrm{P}(f, \tau)$$
其中，$f_{min}$和$f_{max}$分别为肠鸣音信号的有效频率范围下限和上限；


**步骤3**、 基于所述声音强度序列$\mathrm{I}(\tau)$计算信号统计特征，获得声音强度序列的信号均值$\mathrm{\mu_I}$和信号标准差$\mathrm{\sigma_I}$：
$$\mathrm{\mu_I} = \frac{1}{m}\sum_{k=1}^{m} \mathrm{I}(\tau_k)$$
$$\mathrm{\sigma_I} = \sqrt{\frac{1}{m-1}\sum_{k=1}^{m} (\mathrm{I}(\tau_k) - \mathrm{\mu_I})^2}$$
其中，$m$为STFT时间轴上的样本数量，$\mathrm{I}(\tau_k)$为第$k$个STFT时间点的声音强度值；

**步骤4**、 基于所述信号统计特征设置多类型肠鸣音的差异化自适应阈值，依据的公式如下：

$$
\mathrm{Th_{SB}} = \mathrm{\mu_I} + \alpha \cdot \mathrm{\sigma_I}
$$

$$
\mathrm{Th_{CRS}} = \mathrm{\mu_I} + \alpha_{crs} \cdot \mathrm{\sigma_I}
$$

其中，$\mathrm{Th_{SB}}$为短脉冲音SB事件的检测阈值，$\mathrm{Th_{CRS}}$为连续性声音CRS事件的检测阈值，$\alpha$为SB阈值调节参数，取值范围为0.05-0.15，优选值为0.08，$\alpha_{crs}$为CRS阈值调节参数，取值范围为0.02-0.08，优选值为0.05，且满足$\alpha_{crs} < \alpha$的约束条件，所述差异化阈值设置实现不同类型肠鸣音事件的敏感度控制；

**步骤5**、 基于所述SB检测阈值$\mathrm{Th_{SB}}$进行SB候选事件的检测、边界延伸和密度过滤处理，具体包括以下子步骤：

**5.1 事件边界检测和时间范围筛选**

当$\mathrm{I}(\tau) > \mathrm{Th_{SB}}$时，标识为候选区域；利用差分运算检测事件边界：

$$
\mathrm{E_{start}} = \{\tau_k | \mathrm{I}(\tau_k) \leq \mathrm{Th_{SB}} \text{ 且 } \mathrm{I}(\tau_{k+1}) > \mathrm{Th_{SB}}\}
$$

$$
\mathrm{E_{end}} = \{\tau_j | \mathrm{I}(\tau_j) > \mathrm{Th_{SB}} \text{ 且 } \mathrm{I}(\tau_{j+1}) \leq \mathrm{Th_{SB}}\}
$$

筛选事件持续时间$\mathrm{T_{duration}} = \mathrm{E_{end}} - \mathrm{E_{start}}$在$\mathrm{T_{min}}$-$\mathrm{T_{max}}$范围内的事件作为SB候选事件，对所述SB候选事件进行峰值强度验证，要求峰值强度$\mathrm{I_{peak}} > \beta \times \mathrm{Th_{SB}}$；

**5.2 事件边界延伸处理**

事件边界延伸时间计算公式：

$$
\mathrm{\Delta\tau_{ext}} = 0.003 \text{秒}
$$

边界延伸处理：

$$
\mathrm{E_{start}^{ext}} = \max(\tau_1, \mathrm{E_{start}} - \mathrm{\Delta\tau_{ext}})
$$

$$
\mathrm{E_{end}^{ext}} = \min(\tau_m, \mathrm{E_{end}} + \mathrm{\Delta\tau_{ext}})
$$

其中，$\mathrm{\Delta\tau_{ext}}$为边界延伸时间长度，$\tau_1$和$\tau_m$分别为STFT时间轴的起始和结束时间；

**5.3 密度过滤处理**

在时间窗口$\mathrm{T_{window}}$内限制事件数量不超过$\mathrm{N_{max}}$个，对于任意SB候选事件$i$，定义其时间窗口为$[\mathrm{T_{start,i}}, \mathrm{T_{start,i}} + \mathrm{T_{window}}]$，统计该窗口内的事件数量$\mathrm{N_{count}}$：

$$
\mathrm{N_{count}} = |\{j | \mathrm{T_{start,i}} \leq \mathrm{T_{start,j}} \leq \mathrm{T_{start,i}} + \mathrm{T_{window}}\}|
$$

当$\mathrm{N_{count}} \leq \mathrm{N_{max}}$时，保留事件$i$；当$\mathrm{N_{count}} > \mathrm{N_{max}}$时，移除事件$i$；其中，窗口大小$\mathrm{T_{window}}$为1-10秒，最大事件数$\mathrm{N_{max}}$为5-15个，所述密度过滤确保检测结果的合理性和稳定性；

**步骤6**、 多次爆发的识别：对所述SB候选事件进行序列模式识别，采用贪婪搜索算法检测时间间隙在设定范围内的多个SB事件，将符合条件的SB序列标记为多次爆发MB事件，具体包括以下子步骤：

**6.1 SB事件排序和初始化**

对SB事件按时间排序，初始化事件类型标记$\mathrm{Type_i} = \text{"SB"}$，为后续的序列搜索做准备；

**6.2 基于贪婪策略的序列扩展**

采用贪婪扩展策略，从每个未分类的SB事件开始向后搜索符合间隙条件的后续SB事件，计算相邻SB事件间的时间间隙：

$$
\mathrm{Gap_{i,j}} = \mathrm{T_{start,j}} - \mathrm{T_{end,i}}
$$

当$\mathrm{Gap_{min}} \leq \mathrm{Gap_{i,j}} \leq \mathrm{Gap_{max}}$时，将SB事件$j$添加到序列中，形成候选MB序列；

**6.3 序列验证和筛选**

对形成的SB序列进行验证，同时满足以下条件的序列标记为MB事件：

序列长度验证：$\mathrm{N_{min}} \leq |\mathrm{Sequence}| \leq \mathrm{N_{max}}$

序列总持续时间验证：$\mathrm{T_{min}} \leq (\mathrm{T_{end,last}} - \mathrm{T_{start,first}}) \leq \mathrm{T_{max}}$

**6.4 MB事件合并处理**

将验证通过的SB序列合并为单个MB事件，计算MB事件的起始时间、结束时间和强度特征，替换原有的独立SB事件；

其中，$\mathrm{Gap_{min}}$为SB间最小间隙，取值范围-8至5毫秒，$\mathrm{Gap_{max}}$为SB间最大间隙，取值范围50至150毫秒，$\mathrm{N_{min}}$为序列最少SB数量，设定为2个，$\mathrm{N_{max}}$为序列最多SB数量，取值范围5至8个，$\mathrm{T_{min}}$为MB最短总持续时间，取值范围30至50毫秒，$\mathrm{T_{max}}$为MB最长总持续时间，取值范围1000至2000毫秒；

**步骤7**、 连续性声音的检测与事件优先级管理：基于CRS检测阈值进行连续性声音事件的独立检测，并与SB/MB事件进行智能重叠检测和替代处理，具体包括以下子步骤：

**7.1 CRS事件边界检测和时间筛选**

检测超过CRS阈值的信号区域：

$$
\mathrm{Mask_{CRS}} = \{\tau_k | \mathrm{I}(\tau_k) > \mathrm{Th_{CRS}}\}
$$

利用差分运算检测CRS事件边界：

$$
\mathrm{E_{CRS,start}} = \{\tau_k | \mathrm{I}(\tau_k) \leq \mathrm{Th_{CRS}} \text{ 且 } \mathrm{I}(\tau_{k+1}) > \mathrm{Th_{CRS}}\}
$$

$$
\mathrm{E_{CRS,end}} = \{\tau_j | \mathrm{I}(\tau_j) > \mathrm{Th_{CRS}} \text{ 且 } \mathrm{I}(\tau_{j+1}) \leq \mathrm{Th_{CRS}}\}
$$

**7.2 CRS事件边界延伸处理**

对CRS事件进行边界延伸处理：

$$
\mathrm{E_{CRS,start}^{ext}} = \max(\tau_1, \mathrm{E_{CRS,start}} - \mathrm{\Delta\tau_{ext}})
$$

$$
\mathrm{E_{CRS,end}^{ext}} = \min(\tau_m, \mathrm{E_{CRS,end}} + \mathrm{\Delta\tau_{ext}})
$$

对计算得到的CRS事件持续时间$\mathrm{T_{CRS}} = \mathrm{E_{CRS,end}^{ext}} - \mathrm{E_{CRS,start}^{ext}}$进行筛选，保留满足$50\text{ms} \leq \mathrm{T_{CRS}} \leq 3000\text{ms}$的事件作为CRS候选事件；

**7.3 智能重叠检测**

设定重叠容差$\mathrm{\delta_{overlap}} = 0.01$秒，对所有CRS事件和SB/MB事件进行双重循环重叠检测，定义两种重叠情况：

包含关系检测：
$$
\text{Contains}(i,j) = (\mathrm{T_{CRS,start,i}} \leq \mathrm{T_{SB/MB,start,j}} + \mathrm{\delta_{overlap}}) \land (\mathrm{T_{CRS,end,i}} \geq \mathrm{T_{SB/MB,end,j}} - \mathrm{\delta_{overlap}})
$$

部分重叠检测：
$$
\text{Overlaps}(i,j) = (\mathrm{T_{CRS,start,i}} < \mathrm{T_{SB/MB,end,j}} - \mathrm{\delta_{overlap}}) \land (\mathrm{T_{CRS,end,i}} > \mathrm{T_{SB/MB,start,j}} + \mathrm{\delta_{overlap}})
$$

**7.4 事件优先级替代处理**

事件替代决策：当$\text{Contains}(i,j) \lor \text{Overlaps}(i,j)$为真时，标记SB/MB事件$j$为待移除事件；

事件列表更新：移除所有被标记的SB/MB事件，将CRS事件添加到最终事件列表中，并按时间顺序重新排列所有事件；

其中，$\mathrm{T_{CRS,start,i}}$和$\mathrm{T_{CRS,end,i}}$分别为CRS事件$i$的起始和结束时间，$\mathrm{T_{SB/MB,start,j}}$和$\mathrm{T_{SB/MB,end,j}}$分别为SB/MB事件$j$的起始和结束时间，所述智能重叠检测实现CRS事件优先级高于SB/MB事件的替代机制；

**步骤8**、 生成最终检测结果输出，将最终事件列表转换为标准输出格式，生成事件类型标签数组和时间位置矩阵：

$$
\mathrm{labelVals} = \{\mathrm{Type_i} | i = 1, 2, \ldots, N_{total}\}
$$

$$
\mathrm{labelLocs} = \begin{bmatrix}
\mathrm{T_{start,1}} & \mathrm{T_{end,1}} \\
\mathrm{T_{start,2}} & \mathrm{T_{end,2}} \\
\vdots & \vdots \\
\mathrm{T_{start,N_{total}}} & \mathrm{T_{end,N_{total}}}
\end{bmatrix}
$$

对检测结果进行分类计数，计算各类型事件的数量：

$$
\mathrm{N_{SB}} = \sum_{i=1}^{N_{total}} \mathbf{1}(\mathrm{Type_i} = \text{"SB"})
$$

$$
\mathrm{N_{MB}} = \sum_{i=1}^{N_{total}} \mathbf{1}(\mathrm{Type_i} = \text{"MB"})
$$

$$
\mathrm{N_{CRS}} = \sum_{i=1}^{N_{total}} \mathbf{1}(\mathrm{Type_i} = \text{"CRS"})
$$

其中，$\mathrm{Type_i} \in \{\text{"SB"}, \text{"MB"}, \text{"CRS"}\}$为事件类型标识，$N_{total}$为检测到的总事件数，$\mathbf{1}(\cdot)$为指示函数。
